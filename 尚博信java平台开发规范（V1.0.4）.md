# 尚博信Java平台AI开发落地规则（基于V1.0.4规范）
## 一、环境配置与工程初始化规则（AI前置准备）
### 1.1 工具链强制要求
AI需默认使用以下工具版本，无需额外询问：
- **IDE适配**：优先生成IDEA兼容代码（兼容Eclipse），工程结构需符合IDEA导入标准（含`.iml`配置隐含逻辑）
- **基础环境**：JDK 1.8（代码中避免使用1.8+特性，如`var`关键字）、Maven 3.X（`pom.xml`需指定`maven.compiler.source/target=1.8`）
- **版本控制**：Git（代码中需包含Gitignore标准配置，过滤`target/`、`.idea/`、`*.log`等）

### 1.2 工程初始化流程（AI自动生成初始化代码）
#### 1.2.1 Git密钥与代码拉取
AI需生成Git操作指引代码（含注释），示例：
```bash
# 1.检查密钥（AI需提示：无密钥则执行下一步，有则直接上传公钥给@石伟）
cd ~/.ssh && ls
# 2.生成密钥（无密码，避免后续每次拉取输入密码）
ssh-keygen -t rsa -C "<EMAIL>" -N "" -f ~/.ssh/id_rsa
# 3.拉取基础工程（根据业务选择对应仓库）
git clone git@***********:/home/<USER>/git/sunbox.git  # 基础产品
# git clone git@***********:/home/<USER>/git/base-deploy.git  # 管理后台
```

#### 1.2.2 IDEA导入配置（AI需生成工程结构校验代码）
```java
// AI需在工程根目录生成InitProject.java（含工程结构校验逻辑）
public class InitProject {
    public static void main(String[] args) {
        // 校验核心目录是否存在（必含src/main/java、src/main/resources）
        String[] requiredDirs = {"src/main/java", "src/main/resources", "src/test/java"};
        for (String dir : requiredDirs) {
            File file = new File(dir);
            if (!file.exists()) {
                throw new RuntimeException("工程结构异常：缺失必填目录[" + dir + "]，需重新拉取代码");
            }
        }
        System.out.println("工程结构校验通过，可正常开发");
    }
}
```


## 二、项目结构与依赖规则（AI代码组织规范）
### 2.1 模块划分强制要求（AI需按此结构生成包路径）
| 模块        | 包路径前缀                  | 核心职责                          | 禁止操作                          |
|-------------|-----------------------------|-----------------------------------|-----------------------------------|
| api         | `sunbox.api`                | 存放Model（请求/响应）、Feign接口 | 禁止包含业务逻辑、数据库操作      |
| service-cloud| `sunbox.core`（子模块下）   | 微服务核心逻辑（VO/DAO/Action）   | 禁止跨微服务调用（需通过Feign）   |
| web         | `sunbox.web`                | 前端页面（HTML/JS/CSS）、Web配置  | 禁止存放Java业务逻辑              |
| sunbox-core | `sunbox.core`（核心包）     | 公共工具、封装接口（如CommonDao） | 禁止修改核心代码（如CommonVo）    |

### 2.2 核心依赖引入规则（AI自动生成`pom.xml`片段）
#### 2.2.1 必选依赖（AI需默认引入，无需额外声明）
```xml
<!-- sunbox-core核心包（强制使用最新稳定版，禁止自行引入低版本） -->
<dependency>
    <groupId>com.sunbox</groupId>
    <artifactId>sunbox-core</artifactId>
    <version>1.0.4</version> <!-- 与规范版本一致 -->
</dependency>
<!-- SpringBoot基础依赖（版本需兼容sunbox-core） -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
    <version>2.1.6.RELEASE</version> <!-- 文档隐含兼容版本 -->
</dependency>
<!-- Feign依赖（微服务模块必引） -->
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-openfeign</artifactId>
    <version>2.1.3.RELEASE</version>
</dependency>
```

#### 2.2.2 资源文件配置（AI需生成标准配置文件）
- **数据库配置（src/main/resources/application-db.properties）**：
  ```properties
  # AI需使用占位符，避免硬编码（部署时替换）
  core.database.driverClassName=com.mysql.jdbc.Driver
  core.database.name=@core.database.name@
  core.database.url=************************************************************************************************
  core.database.user=@core.database.user@
  core.database.password=@core.database.password@
  ```
- **日志配置（src/main/resources/application-eureka.properties）**：
  ```properties
  # 强制按此级别配置，禁止调整root为debug（避免日志泛滥）
  logging.level.root=error
  logging.level.sunbox=debug
  # 日志路径强制匹配部署规范
  logging.file=/home/<USER>/log/${spring.application.name}.log
  ```


## 三、Java编码规范（AI代码生成核心规则）
### 3.1 命名与格式强制要求（AI自动格式化，无需人工调整）
| 元素        | 命名规则                                  | 示例                                  | 禁止示例                          |
|-------------|-------------------------------------------|---------------------------------------|-----------------------------------|
| 类（Class） | 驼峰命名，首字母大写                      | `ProCategoryVO`、`OrderAction`        | `pro_category_vo`、`orderaction`  |
| 方法（Method） | 驼峰命名，首字母小写，动词开头          | `list()`、`saveOrder()`               | `List()`、`save_order()`          |
| 常量（Constant） | 全大写，下划线分隔，模块前缀            | `USER_USERNAME_EXIST=1201`（用户中心）| `userNameExist=1201`              |
| 字段（Field） | 驼峰命名，首字母小写，数据库下划线转驼峰 | `storeOuCode`（对应数据库`store_ou_code`） | `store_ou_code`                  |
| 包（Package） | 全小写，多级目录用`.`分隔                 | `sunbox.core.vo.product`              | `sunbox.Core.VO.Product`          |

#### 3.1.1 代码格式细节（AI需默认遵循）
- 缩进：2个空格（禁止4个空格）
- 换行：`{`不单独换行，`}`单独换行（如if/for/方法体）
- 空行：方法间空1行，逻辑块间空1行（如if-else、try-catch）
- 注释：类上用`/** */`（含作者、功能描述），方法上用`/** */`（含参数、返回值），单行注释用`//`（行尾或单独一行）

### 3.2 核心组件编码规则（AI需生成标准代码模板）
#### 3.2.1 VO类（数据库映射，AI需继承指定父类）
```java
/**
 * 商品分类VO（对应数据库表：pro_category）
 * <AUTHOR>
 * @date 2024-XX-XX
 */
public class ProCategoryVO extends CommonVo<ProCategoryDAO> { // 单表操作继承CommonVo
    // 1.数据库字段映射（严格对应表字段，下划线转驼峰）
    private String storeOuCode; // 对应store_ou_code（商铺机构编码）
    private String categoryName; // 对应category_name（分类名称）
    private Integer state; // 对应state（状态：1-有效，0-无效）
    
    // 2.扩展字段（非数据库字段，用于接收请求参数）
    private String keyword; // 搜索关键字（前端传参用）
    private List<BrandVO> brandList; // 关联品牌列表（复杂参数）
    
    // 3.Getter/Setter（AI需自动生成，禁止使用Lombok）
    public String getStoreOuCode() {
        return storeOuCode;
    }
    public void setStoreOuCode(String storeOuCode) {
        this.storeOuCode = storeOuCode;
    }
    // 其他Getter/Setter省略...
    
    // 4.分库分表场景需重写（单表操作无需此方法）
    // @Override
    // public String getPartitionCol() {
    //     return "store_ou_code"; // 分库字段（数据库字段名，非驼峰）
    // }
}
```

#### 3.2.2 DAO类（数据访问，AI需绑定VO与表名）
```java
/**
 * 商品分类DAO（对应VO：ProCategoryVO，表：pro_category）
 */
public class ProCategoryDAO extends CommonDao<ProCategoryVO> { // 单表操作继承CommonDao
    /**
     * 绑定数据库表名（严格与数据库一致，全小写+下划线）
     * @return 表名
     */
    @Override
    public String getTableName() {
        return "pro_category"; // 禁止写死为其他表名
    }
}
```

#### 3.2.3 Action类（接口入口，AI需按请求类型分类）
##### （1）JsonController（处理前端JSON请求）
```java
/**
 * 商品分类Action（前端JSON请求入口）
 * 请求地址格式：/product/json/proCategory/[方法名]
 */
public class ProCategoryAction extends BaseAction {
    // 1.绑定VO（AI需初始化，避免空指针）
    private ProCategoryVO proCategoryVO;
    public ProCategoryVO getProCategoryVO() {
        if (proCategoryVO == null) {
            proCategoryVO = new ProCategoryVO();
        }
        return proCategoryVO;
    }
    
    /**
     * 商品分类列表查询
     * @return 分类列表（前端接收JSON格式）
     */
    public List<ProCategoryVO> list() {
        // 2.使用VO调用封装接口（禁止手写SQL）
        ProCategoryVO queryVO = getProCategoryVO();
        // 3.条件查询（AI需生成规范条件拼接）
        queryVO.addConditions("likeCategoryName", "%" + queryVO.getKeyword() + "%") // 模糊查询
               .addAsc("categoryName"); // 按分类名称升序
        return queryVO.select(); // 调用封装的select方法（返回列表）
    }
    
    /**
     * 商品分类保存（新增/修改）
     * @return true-成功，false-失败
     */
    public boolean save() {
        ProCategoryVO saveVO = getProCategoryVO();
        if (saveVO.getId() == null) {
            // 新增：设置默认值（AI需提醒补充业务默认值）
            saveVO.setCreateTime(new DateUtil()); // 使用sunbox工具类（禁止java.util.Date）
            saveVO.setState(1); // 默认有效
            saveVO.insert(); // 封装新增方法
        } else {
            // 修改：按ID更新（必须传ID）
            saveVO.update(); // 封装修改方法
        }
        return true;
    }
}
```

##### （2）RestController（微服务间调用）
```java
/**
 * 订单微服务Action（微服务间Rest请求入口）
 * 请求地址格式：/order/rest/order/[方法名]
 */
@RestController
@RequestMapping("/order/rest/order")
public class OrderAction extends BaseAction {
    // 1.注入Feign接口（跨微服务调用，AI需生成Feign接口模板）
    @Autowired
    private CouponFeignService couponFeignService;
    
    /**
     * 提交订单（调用电子券微服务扣减券）
     * @param requestModel 订单请求参数（来自sunbox.api）
     * @return 订单提交结果（ResponseOrderModel来自sunbox.api）
     */
    @PostMapping("/submit")
    @ActionConstructInit(authRequest = true, // 开启权限校验
                         parameterVoName = "requestModel", 
                         resultModel = ResponseOrderModel.class)
    public OrdPorderVO submit(@RequestBody RequestOrderModel requestModel) {
        // 2.调用电子券微服务（Feign接口）
        ResponseCouponModel couponResult = couponFeignService.deductCoupon(
            requestModel.getUserId(), requestModel.getCouponId()
        );
        if (couponResult.getStatus() != 0) {
            // 3.抛出业务异常（AI需使用指定异常类，错误码符合范围）
            throw new CouponException(CouponException.COUPON_INSUFFICIENT, "电子券不足");
        }
        
        // 4.订单保存（业务逻辑）
        OrdPorderVO orderVO = new OrdPorderVO();
        BeanUtils.copyProperties(requestModel, orderVO); // 复制参数
        orderVO.insert();
        return orderVO;
    }
}
```

##### （3）AppController（处理APP请求）
```java
/**
 * APP订单Action（APP请求入口）
 * 请求地址格式：/app/rest/order/[方法名]
 */
@RestController
@RequestMapping("/app/rest/order")
public class AppOrderAction extends BaseAction {
    /**
     * APP订单列表查询（需验证token）
     * @param token APP用户token（必传）
     * @param pageIndex 页码（默认1）
     * @param pageSize 每页条数（默认10）
     * @return 分页结果（ResponsePageModel来自sunbox.api）
     */
    @PostMapping("/list")
    public ResponsePageModel<OrdPorderVO> appList(
            @RequestParam String token,
            @RequestParam(defaultValue = "1") Integer pageIndex,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        // 1.验证token（AI需调用统一鉴权接口）
        AppUserVO userVO = AppAuthUtil.verifyToken(token);
        if (userVO == null) {
            throw new AppException(AppException.APP_TOKEN_INVALID, "token无效");
        }
        
        // 2.分页查询（按用户ID过滤）
        OrdPorderVO queryVO = new OrdPorderVO();
        queryVO.setUserId(userVO.getId());
        queryVO.setPageIndex(pageIndex);
        queryVO.setPageSize(pageSize);
        List<OrdPorderVO> orderList = queryVO.select();
        int total = queryVO.count(); // 总条数（封装方法）
        
        // 3.构建APP响应格式（严格按规范）
        ResponsePageModel<OrdPorderVO> response = new ResponsePageModel<>();
        response.setStatus(0); // 0-成功，非0-失败
        response.setResult("success");
        response.setData(orderList);
        response.setTotal(total);
        response.setPageIndex(pageIndex);
        response.setPageSize(pageSize);
        return response;
    }
}
```

### 3.3 异常处理规则（AI需按规范抛出异常）
#### 3.3.1 异常类选择与错误码（严格匹配附录）
| 业务模块    | 异常类                | 错误码范围 | 示例（AI需生成）                          |
|-------------|-----------------------|------------|-------------------------------------------|
| 用户中心    | UserException         | 1200-1399  | `throw new UserException(1201, "用户名已存在")` |
| 电子券中心  | CouponException       | 1400-1599  | `throw new CouponException(1402, "电子券已过期")` |
| APP模块     | AppException          | 2200-2239  | `throw new AppException(2201, "token已过期")` |
| 公共异常    | CommonException       | 0-999      | `throw new CommonException(500, "系统繁忙")` |

#### 3.3.2 异常避免规则（AI需自动规避）
- 禁止捕获异常后不处理（如`catch (Exception e) {}`），需抛出业务异常或记录日志
- 禁止抛出`RuntimeException`（需用规范中的业务异常类）
- 禁止在异常信息中包含敏感数据（如密码、数据库地址）


## 四、接口开发规范（AI生成接口代码与文档）
### 4.1 接口定义规则（AI需生成标准请求/响应模型）
#### 4.1.1 请求模型（XxxxModel，存于sunbox.api）
```java
/**
 * 订单提交请求模型（APP/第三方调用订单微服务用）
 * 包路径：sunbox.api.model.order
 */
public class RequestOrderModel {
    // 1.必选字段（标注必填，AI需生成校验逻辑）
    @NotNull(message = "用户ID不能为空") // 需引入validation依赖
    private Long userId; // 用户ID（必传）
    
    @NotNull(message = "商品ID列表不能为空")
    private List<Long> productIds; // 商品ID列表（必传）
    
    // 2.可选字段
    private Long couponId; // 电子券ID（可选）
    private String addressId; // 收货地址ID（可选）
    
    // 3.Getter/Setter（AI需自动生成）
    // ...省略
}
```

#### 4.1.2 响应模型（ResponseXxxxModel，存于sunbox.api）
```java
/**
 * 订单提交响应模型（订单微服务返回给APP/第三方）
 * 包路径：sunbox.api.model.order
 */
public class ResponseOrderModel {
    // 1.公共响应字段（所有响应模型必含）
    private Integer status; // 状态：0-成功，非0-失败（AI需默认赋值0）
    private String result; // 结果描述：success/fail（AI需默认赋值success）
    private String info; // 错误信息（失败时非空，成功时空）
    
    // 2.业务响应字段
    private Long orderId; // 订单ID（成功时返回）
    private BigDecimal orderAmount; // 订单金额（成功时返回）
    private String orderNo; // 订单编号（成功时返回）
    
    // 3.默认构造（AI需生成，设置默认值）
    public ResponseOrderModel() {
        this.status = 0;
        this.result = "success";
    }
    
    // 4.Getter/Setter（AI需自动生成）
    // ...省略
}
```

### 4.2 接口调试规则（AI需生成调试文档）
#### 4.2.1 Postman调试模板（AI需生成JSON示例）
```json
// 请求地址：http://{ip}:{port}/order/rest/order/submit（POST）
// 请求头：Content-Type: application/json
// 请求体（AI需生成正确格式）
{
  "userId": 123456,
  "productIds": [1001, 1002],
  "couponId": 5001,
  "addressId": "ADDR123"
}

// 成功响应（AI需生成预期结果）
{
  "status": 0,
  "result": "success",
  "info": "",
  "orderId": 987654,
  "orderAmount": 299.99,
  "orderNo": "ORD20240520001"
}

// 失败响应（电子券无效）
{
  "status": 1403,
  "result": "fail",
  "info": "电子券无效",
  "orderId": null,
  "orderAmount": null,
  "orderNo": null
}
```

#### 4.2.2 小幺鸡Mock规则（AI需生成Mock配置）
- Mock地址：http://***********:6060/（需提醒开发者注册并联系@姜卫授权）
- Mock响应：需与真实响应格式一致，错误码需符合异常码规范
- Mock场景：需覆盖成功、参数为空、业务异常（如电子券过期）三种场景


## 五、数据库操作规范（AI生成SQL与操作代码）
### 5.1 表设计规则（AI需生成建表SQL）
```sql
-- 商品分类表（pro_category）- AI生成建表语句（严格按规范）
CREATE TABLE `pro_category` (
  -- 1.必含字段（所有表必含）
  `id` bigint(18) NOT NULL AUTO_INCREMENT COMMENT '主键（无业务含义）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识：0-未删，1-已删',
  
  -- 2.业务字段（严格按规范命名：全小写+下划线）
  `store_ou_code` varchar(100) NOT NULL COMMENT '商铺机构编码',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `state` int(1) NOT NULL DEFAULT '1' COMMENT '状态：1-有效，0-无效',
  
  -- 3.索引（AI需生成必要索引）
  PRIMARY KEY (`id`),
  KEY `idx_store_ou_code` (`store_ou_code`) COMMENT '商铺机构编码索引（查询用）',
  KEY `idx_category_name` (`category_name`) COMMENT '分类名称索引（搜索用）'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='商品分类表';
```

### 5.2 数据库操作规则（AI需使用封装接口）
#### 5.2.1 禁止手写SQL（必须使用VO+DAO封装接口）
| 操作类型    | 封装接口使用示例（AI需生成）                          | 禁止操作（AI需提示错误）                  |
|-------------|-------------------------------------------------------|-------------------------------------------|
| 单条查询    | `ProCategoryVO vo = new ProCategoryVO(); vo.setId(1L); vo.loadById();` | `String sql = "select * from pro_category where id=1";` |
| 列表查询    | `List<ProCategoryVO> list = new ProCategoryVO().select();` | `JdbcTemplate.query("select * from ...")`  |
| 新增        | `vo.insert();`                                        | `insert into pro_category(...) values(...)` |
| 修改        | `vo.setCategoryName("新名称"); vo.update();`           | `update pro_category set ... where id=1`   |
| 删除        | `vo.setId(1L); vo.delete();`                          | `delete from pro_category where id=1`      |

#### 5.2.2 分库分表规则（AI需生成对应代码）
- 分表场景：单表数据量超过1000万（AI需提醒开发者评估）
- 分表实现：VO继承`PartitionVo`，DAO继承`PartitionDao`，重写`getPartitionCol()`
```java
// 分库分表VO示例（AI生成）
public class OrderVO extends PartitionVo<OrderDAO> {
    private String userId; // 分库字段（数据库字段：user_id）
    
    // 重写分库字段（返回数据库字段名，非驼峰）
    @Override
    public String getPartitionCol() {
        return "user_id";
    }
    
    // Getter/Setter...
}
```


## 六、安全编码规范（AI自动植入安全逻辑）
### 6.1 权限校验（AI需在接口入口添加）
```java
/**
 * 管理员操作接口（需验证管理员权限）
 */
public class AdminAction extends BaseAction {
    public boolean deleteCategory(Long categoryId) {
        // 1.获取当前登录用户（AI需调用统一鉴权接口）
        AdminVO admin = AdminAuthUtil.getLoginAdmin();
        if (admin == null) {
            throw new SystemException(1001, "请先登录");
        }
        // 2.校验管理员权限（AI需生成权限判断逻辑）
        if (!"ADMIN".equals(admin.getRole())) {
            throw new SystemException(1002, "无删除权限");
        }
        
        // 3.业务操作（删除分类）
        ProCategoryVO vo = new ProCategoryVO();
        vo.setId(categoryId);
        vo.delete();
        return true;
    }
}
```

### 6.2 敏感数据处理（AI需自动脱敏）
```java
/**
 * 用户信息查询（敏感数据脱敏）
 */
public ResponseUserModel getUserInfo(Long userId) {
    UserVO userVO = new UserVO();
    userVO.setId(userId);
    userVO.loadById();
    
    ResponseUserModel response = new ResponseUserModel();
    // 1.手机号脱敏（中间4位替换为*）
    String phone = userVO.getPhone();
    if (StringUtils.isNotBlank(phone) && phone.length() == 11) {
        response.setPhone(phone.substring(0, 3) + "****" + phone.substring(7));
    }
    // 2.身份证脱敏（中间8位替换为*）
    String idCard = userVO.getIdCard();
    if (StringUtils.isNotBlank(idCard) && idCard.length() == 18) {
        response.setIdCard(idCard.substring(0, 6) + "********" + idCard.substring(14));
    }
    // 3.密码禁止返回（AI需过滤密码字段）
    response.setUserName(userVO.getUserName());
    // ...其他非敏感字段
    return response;
}
```

### 6.3 SQL注入防护（AI需使用参数化查询）
- 禁止字符串拼接SQL（AI需提示错误）
- 必须使用VO的`addConditions()`或`appendSql()+addSqlBind()`
```java
// 正确示例（AI生成）
public List<ProCategoryVO> search(String keyword) {
    ProCategoryVO vo = new ProCategoryVO();
    // 参数化模糊查询（避免SQL注入）
    vo.addConditions("likeCategoryName", "%" + keyword + "%");
    return vo.select();
}

// 错误示例（AI需提示禁止）
// public List<ProCategoryVO> search(String keyword) {
//     String sql = "select * from pro_category where category_name like '%" + keyword + "%'";
//     return vo.appendSql(sql).select(); // 禁止直接拼接关键字
// }
```


## 七、部署与测试规范（AI生成部署脚本与测试用例）
### 7.1 部署脚本（AI需生成Linux部署脚本）
#### 7.1.1 手工部署脚本（run.sh）
```bash
#!/bin/bash
# 商品分类微服务部署脚本（AI生成，需放在client/bin目录）
# 部署路径：/home/<USER>/client（严格按规范）

# 1.停止旧服务（AI需根据服务名停止）
echo "停止商品分类微服务..."
ps -ef | grep pro-category-service | grep -v grep | awk '{print $2}' | xargs kill -9 2>/dev/null
sleep 2

# 2.启动新服务（日志路径严格按规范）
echo "启动商品分类微服务..."
nohup java -jar /home/<USER>/client/lib/pro-category-service-1.0.4.jar \
  --spring.profiles.active=prod \
  --logging.file=/home/<USER>/log/pro-category-service.log >/dev/null 2>&1 &

# 3.检查启动状态
sleep 5
if ps -ef | grep pro-category-service | grep -v grep >/dev/null; then
    echo "商品分类微服务启动成功"
else
    echo "商品分类微服务启动失败，查看日志：/home/<USER>/log/pro-category-service.log"
fi
```

### 7.2 测试用例（AI需生成禅道兼容模板）
#### 7.2.1 商品分类新增测试用例
| 用例ID    | 用例名称                | 前置条件                | 测试步骤                                                                 | 预期结果                                                                 | 实际结果 | 状态   |
|-----------|-------------------------|-------------------------|--------------------------------------------------------------------------|--------------------------------------------------------------------------|----------|--------|
| TC-PRO-001 | 新增有效商品分类        | 1.管理员已登录<br>2.分类名称未重复 | 1.进入商品分类页面<br>2.点击“新增”按钮<br>3.输入分类名称“测试分类”、商铺编码“2000”<br>4.点击“保存” | 1.提示“保存成功”<br>2.数据库pro_category表新增一条记录<br>3.列表页显示“测试分类” |          | 未执行 |
| TC-PRO-002 | 新增重复商品分类        | 1.管理员已登录<br>2.分类名称“测试分类”已存在 | 1.进入商品分类页面<br>2.点击“新增”按钮<br>3.输入分类名称“测试分类”、商铺编码“2000”<br>4.点击“保存” | 1.提示“分类名称已存在”（错误码：3201，对应商品中心异常）<br>2.数据库无新增记录 |          | 未执行 |


## 八、AI开发禁忌清单（需严格规避）
1. 禁止使用非规范工具类（如`java.util.Date`，必须用`sunbox.core.util.DateUtil`）
2. 禁止修改sunbox-core核心代码（如CommonVo、CommonDao）
3. 禁止跨模块存放代码（如将API的Model放在service-cloud）
4. 禁止硬编码配置（如数据库地址、密码，必须用占位符）
5. 禁止日志输出敏感数据（如密码、token，需过滤）
6. 禁止使用Lombok（需手动生成Getter/Setter）
7. 禁止接口响应格式不统一（必须包含status/result/info字段）
8. 禁止分库分表场景不重写`getPartitionCol()`
9. 禁止异常信息包含技术细节（如“NullPointerException at XXX”）
10. 禁止部署路径与日志路径不按规范（必须在/home/<USER>
11. 禁止部署脚本不按规范（必须放在client/bin目录）